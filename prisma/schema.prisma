generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model bannerdata {
  id          Int     @id @default(autoincrement())
  title       String? @db.VarChar(255)
  banner_text String? @db.VarChar(255)
  imgpath     String? @db.VarChar(255)
}

model sponserimg {
  id      Int     @id @default(autoincrement())
  imgpath String? @db.VarChar(255)
}

model dresstyle {
  id       Int     @id @default(autoincrement())
  title    String? @db.VarChar(255)
  imgpath  String? @db.VarChar(255)
  colstart String? @db.VarChar(255)
  colspan  String? @db.VarChar(255)
}

model dresscategory {
  id       String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  category String? @db.VarChar(250)
}
